import os
import json
import subprocess
from datetime import datetime
from airflow.decorators import dag, task
from airflow.hooks.base import BaseHook


@dag(
    dag_id='test_file_conversion',
    schedule_interval=None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['testing']
)
def test_file_conversion():
    """Simple test DAG for file conversion: download gz -> unzip -> bzip2 -> upload"""
    
    @task()
    def convert_file():
        """Download gz file, unzip, compress with bzip2, upload to S3"""
        
        # File paths - update these for your actual files
        source_file = 's3://your-source-bucket/test-file.gz'
        destination_file = 's3://your-destination-bucket/test-file.bz2'
        
        # Get AWS credentials from Airflow connection
        try:
            conn = BaseHook.get_connection('aws_default')  # or your AWS connection ID
            conn_extra = json.loads(conn.get_extra())
            env = os.environ.copy()
            env.update({
                'AWS_REGION': conn_extra.get('region_name', 'us-east-1')
            })
        except:
            env = os.environ.copy()  # Use default environment if connection fails
        
        # Command as requested: download -> unzip -> bzip2 -> upload
        cmd = f'aws s3 cp {source_file} - | zcat | lbzip2 -c | aws s3 cp - {destination_file}'
        
        print(f'Executing: {cmd}')
        print(f'Source: {source_file}')
        print(f'Destination: {destination_file}')
        
        # Execute with subprocess.Popen
        process = subprocess.Popen(
            cmd,
            shell=True,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Get output
        stdout, stderr = process.communicate()
        
        # Print results
        print(f'Return code: {process.returncode}')
        print(f'STDOUT: {stdout.decode("utf-8") if stdout else "No output"}')
        print(f'STDERR: {stderr.decode("utf-8") if stderr else "No errors"}')
        
        # Check for errors
        if process.returncode != 0:
            raise Exception(f'Command failed with return code {process.returncode}: {stderr.decode("utf-8")}')
        
        if stderr and any(x in stderr.decode("utf-8").lower() for x in ['error', 'failed']):
            raise Exception(f'Command completed but with errors: {stderr.decode("utf-8")}')
        
        print('File conversion completed successfully!')
        return {
            'source': source_file,
            'destination': destination_file,
            'return_code': process.returncode
        }
    
    convert_file()


# Create the DAG instance
test_dag = test_file_conversion()
